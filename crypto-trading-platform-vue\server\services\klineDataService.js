const KlineData = require('../models/KlineData');
const binancePythonTradeService = require('./binancePythonTradeService');
const marketDataService = require('./marketDataService');
const okxService = require('./okxService');

/**
 * K线数据服务
 * 负责K线数据的获取、存储和查询
 */
class KlineDataService {
  
  /**
   * 从数据库获取K线数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} startTime 开始时间戳
   * @param {number} endTime 结束时间戳
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} K线数据数组
   */
  async getKlineDataFromDB(exchange, symbol, timeframe, startTime, endTime, limit = 1000) {
    try {
      console.log(`从数据库获取K线数据: ${exchange} ${symbol} ${timeframe}`);
      
      const klines = await KlineData.getKlineData(
        exchange, 
        symbol, 
        timeframe, 
        startTime, 
        endTime, 
        limit
      );
      
      console.log(`数据库返回 ${klines.length} 条K线数据`);
      return klines;
    } catch (error) {
      console.error('从数据库获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 从API获取K线数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} limit 限制数量
   * @param {string} userId 用户ID（用于获取API密钥）
   * @returns {Promise<Array>} K线数据数组
   */
  async getKlineDataFromAPI(exchange, symbol, timeframe, limit = 1000, userId = null) {
    try {
      console.log(`从API获取K线数据: ${exchange} ${symbol} ${timeframe}`);
      
      if (exchange === 'binance') {
        const result = await binancePythonTradeService.getBinanceKlines(symbol, timeframe, limit);
        if (result.success && result.klines) {
          return result.klines.sort((a, b) => a.timestamp - b.timestamp);
        } else {
          throw new Error(result.error || '获取币安K线数据失败');
        }
      } else if (exchange === 'okx') {
        if (!userId) {
          throw new Error('OKX需要用户ID来获取API密钥');
        }
        
        const apiKeys = okxService.getUserApiKeys(userId);
        const credentials = {
          apiKey: apiKeys.apiKey,
          secret: apiKeys.secretKey,
          password: apiKeys.passphrase
        };
        
        const klines = await marketDataService.getKlines(symbol, timeframe, limit, credentials);
        return klines.sort((a, b) => a.timestamp - b.timestamp);
      }
      
      throw new Error(`不支持的交易所: ${exchange}`);
    } catch (error) {
      console.error('从API获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取K线数据（优先从数据库，不足时从API补充）
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} startTime 开始时间戳
   * @param {number} endTime 结束时间戳
   * @param {number} limit 限制数量
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} K线数据数组
   */
  async getKlineData(exchange, symbol, timeframe, startTime, endTime, limit = 1000, userId = null) {
    try {
      // 首先尝试从数据库获取
      let dbKlines = await this.getKlineDataFromDB(exchange, symbol, timeframe, startTime, endTime, limit);
      
      // 检查数据库数据是否足够
      if (dbKlines.length >= limit) {
        console.log(`数据库数据充足，返回 ${dbKlines.length} 条记录`);
        return dbKlines;
      }
      
      // 如果数据库数据不足，从API获取补充
      console.log(`数据库数据不足（${dbKlines.length}/${limit}），从API获取补充数据`);
      
      try {
        const apiKlines = await this.getKlineDataFromAPI(exchange, symbol, timeframe, limit, userId);
        
        // 合并数据并去重
        const allKlines = [...dbKlines, ...apiKlines];
        const uniqueKlines = this.removeDuplicateKlines(allKlines);
        
        // 按时间排序
        uniqueKlines.sort((a, b) => a.timestamp - b.timestamp);
        
        // 应用时间范围过滤
        let filteredKlines = uniqueKlines;
        if (startTime || endTime) {
          filteredKlines = uniqueKlines.filter(kline => {
            if (startTime && kline.timestamp < startTime) return false;
            if (endTime && kline.timestamp > endTime) return false;
            return true;
          });
        }
        
        // 限制数量
        if (filteredKlines.length > limit) {
          filteredKlines = filteredKlines.slice(-limit);
        }
        
        console.log(`合并后返回 ${filteredKlines.length} 条K线数据`);
        return filteredKlines;
        
      } catch (apiError) {
        console.warn('API获取失败，仅返回数据库数据:', apiError.message);
        return dbKlines;
      }
      
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 存储K线数据到数据库
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {Array} klines K线数据数组
   * @returns {Promise<Object>} 存储结果
   */
  async storeKlineData(exchange, symbol, timeframe, klines) {
    try {
      if (!klines || klines.length === 0) {
        return { insertedCount: 0, errors: [] };
      }
      
      // 格式化数据
      const formattedKlines = klines.map(kline => ({
        exchange,
        symbol,
        timeframe,
        timestamp: kline.timestamp,
        open: kline.open,
        high: kline.high,
        low: kline.low,
        close: kline.close,
        volume: kline.volume
      }));
      
      console.log(`存储 ${formattedKlines.length} 条K线数据到数据库: ${exchange} ${symbol} ${timeframe}`);
      
      const result = await KlineData.bulkInsertKlines(formattedKlines);
      console.log(`存储完成，插入/更新 ${result.insertedCount} 条记录`);
      
      return result;
    } catch (error) {
      console.error('存储K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 去除重复的K线数据
   * @param {Array} klines K线数据数组
   * @returns {Array} 去重后的K线数据数组
   */
  removeDuplicateKlines(klines) {
    const seen = new Set();
    return klines.filter(kline => {
      const key = `${kline.timestamp}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 获取数据统计信息
   * @returns {Promise<Array>} 统计信息
   */
  async getDataStats() {
    try {
      return await KlineData.getDataStats();
    } catch (error) {
      console.error('获取数据统计失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期数据
   * @param {number} daysToKeep 保留天数
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupOldData(daysToKeep = 365) {
    try {
      console.log(`清理 ${daysToKeep} 天前的K线数据`);
      const result = await KlineData.cleanupOldData(daysToKeep);
      console.log(`清理完成，删除 ${result.deletedCount} 条记录`);
      return result;
    } catch (error) {
      console.error('清理过期数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取最新的K线数据时间戳
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @returns {Promise<number|null>} 最新时间戳
   */
  async getLatestTimestamp(exchange, symbol, timeframe) {
    try {
      const latestKline = await KlineData.getLatestKline(exchange, symbol, timeframe);
      return latestKline ? latestKline.timestamp : null;
    } catch (error) {
      console.error('获取最新时间戳失败:', error);
      return null;
    }
  }
}

module.exports = new KlineDataService();
